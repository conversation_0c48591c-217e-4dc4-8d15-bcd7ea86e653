import { createAuthClient } from "better-auth/react";

export const authClient = createAuthClient({
  baseURL: process.env.NEXT_PUBLIC_BETTER_AUTH_URL || "http://localhost:3000",
  fetchOptions: {
    onError: (e) => {
      if (e.error.status === 429) console.error("Rate limit exceeded");
      else if (e.error.status === 401) console.error("Unauthorized access");
      else if (e.error.status >= 500) console.error("Server error:", e.error.message);
    },
    onRequest: (context) => context,
    onSuccess: (_context) => {},
  },
});

export type AuthError = {
  message: string;
  status?: number;
  code?: string;
};

export const handleAuthError = (error: any): AuthError => {
  if (error?.error) {
    return {
      message: error.error.message || "Authentication failed",
      status: error.error.status,
      code: error.error.code,
    };
  }

  if (error?.message)
    return {
      message: error.message,
    };

  return {
    message: "An unexpected error occurred",
  };
};
