import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const badgeVariants = cva(
  "inline-flex items-center justify-center rounded-none border-none px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden relative",
  {
    variants: {
      variant: {
        default: "bg-foreground text-background [a&]:hover:bg-foreground/90",
        secondary: "bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",
        destructive:
          "bg-foreground text-background [a&]:hover:bg-foreground/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",
        outline: "bg-foreground text-background [a&]:hover:bg-foreground/90 [a&]:hover:text-background",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

const Badge = ({
  className,
  variant,
  asChild = false,
  children,
  ...props
}: React.ComponentProps<"span"> & VariantProps<typeof badgeVariants> & { asChild?: boolean }) => {
  const Comp = asChild ? Slot : "span";
  return (
    <Comp data-slot="badge" className={cn(badgeVariants({ variant }), className)} {...props}>
      {children}
      {/* 8bit pixelated borders for badges */}
      <div className="absolute -top-0.5 w-1/2 left-0.5 h-0.5 bg-foreground dark:bg-ring" />
      <div className="absolute -top-0.5 w-1/2 right-0.5 h-0.5 bg-foreground dark:bg-ring" />
      <div className="absolute -bottom-0.5 w-1/2 left-0.5 h-0.5 bg-foreground dark:bg-ring" />
      <div className="absolute -bottom-0.5 w-1/2 right-0.5 h-0.5 bg-foreground dark:bg-ring" />
      <div className="absolute top-0 left-0 size-0.5 bg-foreground dark:bg-ring" />
      <div className="absolute top-0 right-0 size-0.5 bg-foreground dark:bg-ring" />
      <div className="absolute bottom-0 left-0 size-0.5 bg-foreground dark:bg-ring" />
      <div className="absolute bottom-0 right-0 size-0.5 bg-foreground dark:bg-ring" />
      <div className="absolute top-0.5 -left-0.5 h-[calc(100%-4px)] w-0.5 bg-foreground dark:bg-ring" />
      <div className="absolute top-0.5 -right-0.5 h-[calc(100%-4px)] w-0.5 bg-foreground dark:bg-ring" />
    </Comp>
  );
};

export { Badge, badgeVariants };
