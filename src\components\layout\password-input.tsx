"use client";

import React from "react";
import { Input } from "../ui/input";
import { Eye, EyeOff } from "lucide-react";

export const PasswordInput = (props: React.ComponentProps<typeof Input>) => {
  const [visible, setVisible] = React.useState(false);
  return (
    <div className="relative">
      <Input type={visible ? "text" : "password"} {...props} />
      <button
        type="button"
        onClick={() => setVisible(!visible)}
        className="absolute right-4 top-1/2 -translate-y-1/2 text-foreground hover:text-foreground/60">
        {visible ? <EyeOff size={18} /> : <Eye size={18} />}
      </button>
    </div>
  );
};
