"use client";

import { io, Socket } from "socket.io-client";
import type { ServerToClientEvents, ClientToServerEvents } from "@/types/sockets";

class SocketManager {
  private socket: Socket<ServerToClientEvents, ClientToServerEvents> | null = null;
  private url: string;
  constructor() {
    this.url = process.env.NEXT_PUBLIC_MESSAGE_SOCKET_URL as string;
  }

  connect(): Socket<ServerToClientEvents, ClientToServerEvents> {
    if (!this.socket) {
      this.socket = io(this.url, {
        autoConnect: true,
        transports: ["websocket", "polling"],
      });
      this.socket.on("connect", () => {
        console.log("Connected to socket server:", this.socket?.id);
      });
      this.socket.on("disconnect", (reason) => {
        console.log("Disconnected from socket server:", reason);
      });
      this.socket.on("connect_error", (error) => {
        console.error("Socket connection error:", error);
      });
    }

    if (!this.socket.connected) this.socket.connect();
    return this.socket;
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  getSocket(): Socket<ServerToClientEvents, ClientToServerEvents> | null {
    return this.socket;
  }
}

// Create a singleton instance
const socketManager = new SocketManager();

export const socket = socketManager.connect();
export { socketManager };
