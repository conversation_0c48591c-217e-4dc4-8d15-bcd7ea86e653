"use server";

import { eq, isNotNull, and } from "drizzle-orm";
import { db } from "@/db";
import { account, user } from "@/db/schema";
import { auth } from "@/lib/auth";
import { redis } from "@/lib/redis";

const getCacheKey = (prefix: string, userId: string) => `user-${prefix}:${userId}`;

const invalidateFriendSearchCaches = async () => {
  try {
    const keys = await redis.keys("friend-search:*");
    if (keys.length > 0) await redis.del(keys as [string, ...string[]]);
  } catch (error) {
    console.error("Error invalidating friend search caches:", error);
  }
};

export const getBio = async (userId: string): Promise<string> => {
  const cacheKey = getCacheKey("bio", userId);

  try {
    const cached = await redis.get(cacheKey);
    if (cached) return cached;
  } catch (error) {
    console.error("Redis get error:", error);
  }

  const result = await db.select({ bio: user.bio }).from(user).where(eq(user.id, userId));
  const bio = result[0]?.bio || "";

  try {
    await redis.set(cacheKey, bio, { EX: Number(process.env.CACHE_TTL) });
  } catch (error) {
    console.error("Redis set error:", error);
  }

  return bio;
};

export const updateBio = async (userId: string, newBio: string): Promise<void> => {
  if (newBio.length > 512) throw new Error("Bio cannot be longer than 512 characters");
  await db.update(user).set({ bio: newBio }).where(eq(user.id, userId));
  try {
    await Promise.all([
      redis.del(getCacheKey("bio", userId)),
      invalidateFriendSearchCaches(), // Invalidate friend search caches that might contain this user's bio
    ]);
  } catch (error) {
    console.error("Redis cache invalidation error:", error);
  }
};

export const getName = async (userId: string): Promise<string> => {
  const cacheKey = getCacheKey("name", userId);
  try {
    const cached = await redis.get(cacheKey);
    if (cached) return cached;
  } catch (error) {
    console.error("Redis get error:", error);
  }
  const result = await db.select({ name: user.name }).from(user).where(eq(user.id, userId));
  const name = result[0]?.name || "";
  await redis.set(cacheKey, name, { EX: Number(process.env.CACHE_TTL) });
  return name;
};

export const updateName = async (userId: string, newName: string): Promise<void> => {
  if (newName.length > 64) throw new Error("Name cannot be longer than 64 characters");
  if (newName.trim().length < 6) throw new Error("Name cannot be less than 6 characters");
  await db.update(user).set({ name: newName }).where(eq(user.id, userId));
  try {
    await Promise.all([
      redis.del(getCacheKey("name", userId)),
      invalidateFriendSearchCaches(), // Invalidate friend search caches that might contain this user's name
    ]);
  } catch (error) {
    console.error("Redis cache invalidation error:", error);
  }
};

export const hasPassword = async (userId: string): Promise<boolean> => {
  const cacheKey = getCacheKey("hasPassword", userId);
  try {
    const cached = await redis.get(cacheKey);
    if (cached !== null) return cached === "true";
  } catch (error) {
    console.error("Redis get error:", error);
  }

  const result = await db
    .select({ id: account.userId })
    .from(account)
    .where(and(eq(account.userId, userId), isNotNull(account.password)))
    .limit(1);
  const hasPass = result.length > 0;
  await redis.set(cacheKey, hasPass.toString(), { EX: Number(process.env.CACHE_TTL) });
  return hasPass;
};

export const setNewPassword = async (newPassword: string, headers: Headers): Promise<void> => {
  if (newPassword.length < 8) throw new Error("Password must be at least 8 characters long");
  if (newPassword.length > 128) throw new Error("Password cannot be longer than 128 characters");
  await auth.api.setPassword({
    body: { newPassword },
    headers,
  });
  const session = await auth.api.getSession({ headers });
  if (session?.user?.id) {
    try {
      await redis.del(getCacheKey("hasPassword", session.user.id));
    } catch (error) {
      console.error("Redis cache invalidation error:", error);
    }
  }
};

export const updatePassword = async (
  currentPassword: string,
  newPassword: string,
  headers: Headers,
  revoke: boolean
): Promise<{
  token: string | null;
  user: {
    id: string;
    email: string;
    name: string;
    image: string | null | undefined;
    emailVerified: boolean;
    createdAt: Date;
    updatedAt: Date;
  };
}> => {
  if (newPassword.length < 8) throw new Error("Password must be at least 8 characters long");
  if (newPassword.length > 128) throw new Error("Password cannot be longer than 128 characters");
  const data = await auth.api.changePassword({
    body: {
      newPassword: newPassword,
      currentPassword: currentPassword,
      revokeOtherSessions: revoke,
    },
    headers: headers,
  });
  if (data.user?.id)
    try {
      await redis.del(getCacheKey("hasPassword", data.user.id));
    } catch (error) {
      console.error("Redis cache invalidation error:", error);
    }

  return data;
};

export const updateEmail = async (userId: string, newEmail: string): Promise<void> => {
  if (newEmail.length > 256) throw new Error("Email cannot be longer than 256 characters");
  if (!newEmail.match(/^\S+@\S+\.\S+$/)) throw new Error("Please enter a valid email address");
  await db.update(user).set({ email: newEmail }).where(eq(user.id, userId));
};
