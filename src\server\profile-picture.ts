"use server";

import { db } from "@/db";
import { user } from "@/db/schema";
import { AVATAR_BUCKET, createBucket, getUrl, minio } from "@/lib/minio";
import { eq } from "drizzle-orm";

export const uploadAvatar = async (formData: FormData): Promise<{ success: boolean; path?: string }> => {
  const file = formData.get("file") as File;
  const userId = formData.get("userId") as string;
  if (!file || !userId) return { success: false };
  const ext = file.name.split(".").findLast(() => true);
  if (!ext) return { success: false };
  const path = `${userId}.${ext}`;
  try {
    await createBucket(AVATAR_BUCKET, false);
    await minio.putObject(AVATAR_BUCKET, path, Buffer.from(await file.arrayBuffer()));
    const url = await getUrl(AVATAR_BUCKET, path, false);
    if (!url) return { success: false };
    await db.update(user).set({ image: url }).where(eq(user.id, userId));
    return { success: true, path: url };
  } catch (error) {
    console.error("Upload failed:", error);
    return { success: false };
  }
};
