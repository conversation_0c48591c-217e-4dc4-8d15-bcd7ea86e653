import { useAvatar } from "@/components/layout/avatar-provider";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { cn } from "@/lib/utils";

export default ({ size = "sm", img, className }: { size?: "sm" | "md" | "lg"; img?: string | null; className?: string }) => {
  const cls: Record<typeof size, string> = {
    sm: "size-8",
    md: "size-12",
    lg: "size-full",
  };

  const image = img === null || img === undefined ? useAvatar().image : img;
  return (
    <Avatar className={cn(cls[size], className)}>
      <AvatarImage className={cls[size]} src={image} alt="profile-picture" />
      <AvatarFallback>CN</AvatarFallback>
    </Avatar>
  );
};
