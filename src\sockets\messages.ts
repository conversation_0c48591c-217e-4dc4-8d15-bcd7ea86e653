"use server";

import { createServer } from "http";
import { Server } from "socket.io";
import type { ServerToClientEvents, ClientToServerEvents, InterServerEvents, SocketData } from "@/types/sockets";

const httpServer = createServer();
const io = new Server<ClientToServerEvents, ServerToClientEvents, InterServerEvents, SocketData>(httpServer, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"],
  },
});

io.on("connection", (socket) => {
  console.log("User connected:", socket.id);

  // send server time immediately
  socket.emit("serverTime", { time: Date.now() });

  const interval = setInterval(() => {
    socket.emit("serverTime", { time: Date.now() });
  }, 1000);

  socket.on("disconnect", () => {
    clearInterval(interval);
    console.log("User disconnected:", socket.id);
  });
});

const PORT = process.env.SOCKET_PORT as string;
httpServer.listen(PORT, () => {
  console.log(`Socket server running on :${PORT}`);
});
