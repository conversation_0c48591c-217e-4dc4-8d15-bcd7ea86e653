"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "@/components/ui/toast";
import { authClient } from "@/lib/auth-client";

export default () => {
  return (
    <div>
      <Button
        onClick={() => {
          authClient.signOut();
          window.location.href = "/";
        }}
        variant={"destructive"}>
        Sign Out
      </Button>
      <Button onClick={() => toast("Hello World")}>Toast</Button>
    </div>
  );
};
