"use client";

import * as React from "react";
import * as ToggleGroupPrimitive from "@radix-ui/react-toggle-group";
import { type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";
import { toggleVariants } from "@/components/ui/toggle";

const ToggleGroupContext = React.createContext<VariantProps<typeof toggleVariants>>({
  size: "default",
  variant: "default",
});

const ToggleGroup = ({
  className,
  variant,
  size,
  children,
  ...props
}: React.ComponentProps<typeof ToggleGroupPrimitive.Root> & VariantProps<typeof toggleVariants>) => {
  return (
    <ToggleGroupPrimitive.Root
      data-slot="toggle-group"
      data-variant={variant}
      data-size={size}
      className={cn("group/toggle-group flex w-fit items-center rounded-none gap-3 m-1.5", className)}
      {...props}>
      <ToggleGroupContext.Provider value={{ variant, size }}>{children}</ToggleGroupContext.Provider>
    </ToggleGroupPrimitive.Root>
  );
};

const ToggleGroupItem = ({
  className,
  children,
  variant,
  size,
  ...props
}: React.ComponentProps<typeof ToggleGroupPrimitive.Item> & VariantProps<typeof toggleVariants>) => {
  const context = React.useContext(ToggleGroupContext);
  const currentVariant = context.variant || variant;

  return (
    <ToggleGroupPrimitive.Item
      data-slot="toggle-group-item"
      data-variant={currentVariant}
      data-size={context.size || size}
      className={cn(
        toggleVariants({
          variant: currentVariant,
          size: context.size || size,
        }),
        "relative min-w-0 flex-1 shrink-0 rounded-none shadow-none focus:z-10 focus-visible:z-10 transition-transform active:translate-x-1 active:translate-y-1",
        className
      )}
      {...props}>
      {children}

      {/* 8bit pixelated borders for outline variant */}
      {currentVariant === "outline" && (
        <>
          <div className="absolute inset-0 -my-1.5 border-y-6 border-foreground dark:border-ring pointer-events-none" aria-hidden="true" />
          <div className="absolute inset-0 -mx-1.5 border-x-6 border-foreground dark:border-ring pointer-events-none" aria-hidden="true" />
        </>
      )}
    </ToggleGroupPrimitive.Item>
  );
};

export { ToggleGroup, ToggleGroupItem };
