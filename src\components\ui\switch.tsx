"use client";

import * as React from "react";
import * as SwitchPrimitive from "@radix-ui/react-switch";

import { cn } from "@/lib/utils";

const Switch = ({ className, ...props }: React.ComponentProps<typeof SwitchPrimitive.Root>) => {
  return (
    <div className="relative m-1.5">
      <SwitchPrimitive.Root
        data-slot="switch"
        className={cn(
          "peer data-[state=checked]:bg-foreground data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-none border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",
          className
        )}
        {...props}>
        <SwitchPrimitive.Thumb
          data-slot="switch-thumb"
          className={cn(
            "bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-background pointer-events-none block size-4 rounded-none ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0"
          )}
        />
      </SwitchPrimitive.Root>

      {/* 8bit pixelated borders for switch */}
      <div className="absolute -top-0.5 w-1/2 left-0.5 h-0.5 bg-foreground dark:bg-ring pointer-events-none" />
      <div className="absolute -top-0.5 w-1/2 right-0.5 h-0.5 bg-foreground dark:bg-ring pointer-events-none" />
      <div className="absolute -bottom-0.5 w-1/2 left-0.5 h-0.5 bg-foreground dark:bg-ring pointer-events-none" />
      <div className="absolute -bottom-0.5 w-1/2 right-0.5 h-0.5 bg-foreground dark:bg-ring pointer-events-none" />
      <div className="absolute top-0 left-0 size-0.5 bg-foreground dark:bg-ring pointer-events-none" />
      <div className="absolute top-0 right-0 size-0.5 bg-foreground dark:bg-ring pointer-events-none" />
      <div className="absolute bottom-0 left-0 size-0.5 bg-foreground dark:bg-ring pointer-events-none" />
      <div className="absolute bottom-0 right-0 size-0.5 bg-foreground dark:bg-ring pointer-events-none" />
      <div className="absolute top-0.5 -left-0.5 h-[calc(100%-4px)] w-0.5 bg-foreground dark:bg-ring pointer-events-none" />
      <div className="absolute top-0.5 -right-0.5 h-[calc(100%-4px)] w-0.5 bg-foreground dark:bg-ring pointer-events-none" />
    </div>
  );
};

export { Switch };
