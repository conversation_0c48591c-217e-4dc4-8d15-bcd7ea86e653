"use client";

import { useEffect, useState } from "react";
import { socket } from "@/lib/messages";

export default () => {
  const [serverTime, setServerTime] = useState<string>("");

  useEffect(() => {
    socket.connect();

    socket.on("serverTime", (data) => {
      setServerTime(new Date(data.time).toLocaleTimeString());
    });

    return () => {
      socket.off("serverTime");
      socket.disconnect();
    };
  }, []);

  return (
    <div className="p-4 rounded-2xl shadow-md bg-white text-black">
      <h2 className="text-lg font-bold mb-2">Server Time</h2>
      <p className="text-xl">{serverTime || "Waiting..."}</p>
    </div>
  );
};
