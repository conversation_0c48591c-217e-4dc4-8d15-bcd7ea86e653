name: Build

on:
  pull_request:
  push:
    branches: ["main"]

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      DATABASE_HOST: localhost
      DATABASE_PORT: 5432
      DATABASE_USER: test
      DATABASE_PASSWORD: test
      DATABASE_DB: test
      REDIS_PORT: 6379
      CACHE_TTL: test
      S3_PORT: 9000
      S3_CONSOLE_PORT: 9001
      S3_ROOT_USER: test
      S3_ROOT_PASSWORD: test
      BETTER_AUTH_SECRET: test
      BETTER_AUTH_URL: http://localhost:3000
      GOOGLE_CLIENT_ID: test
      GOOGLE_CLIENT_SECRET: test
      GITHUB_CLIENT_ID: test
      GITHUB_CLIENT_SECRET: test
      WEBSOCKET_URL: test
      RESEND_API_KEY: test

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: npm

      - name: Restore Next.js build cache
        uses: actions/cache@v4
        with:
          path: .next/cache
          key: ${{ runner.os }}-nextjs-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-nextjs-

      - run: npm ci

      - name: Build
        run: npm run build

      - name: Save Next.js build cache
        uses: actions/cache@v4
        with:
          path: .next/cache
          key: ${{ runner.os }}-nextjs-${{ github.sha }}
