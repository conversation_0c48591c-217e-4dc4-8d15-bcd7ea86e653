"use client";

import React, { createContext, useContext, useState, ReactNode } from "react";

interface AvatarContextType {
  image: string;
  setImage: (img: string) => void;
}

const AvatarContext = createContext<AvatarContextType | undefined>(undefined);

export const useAvatar = () => {
  const ctx = useContext(AvatarContext);
  if (!ctx) throw new Error("useAvatar must be used within AvatarProvider");
  return ctx;
};

export const AvatarProvider = ({ initialImage, children }: { initialImage: string; children: ReactNode }) => {
  const [image, setImage] = useState(initialImage);
  return <AvatarContext.Provider value={{ image, setImage }}>{children}</AvatarContext.Provider>;
};
