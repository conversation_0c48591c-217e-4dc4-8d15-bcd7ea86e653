"use client";

import { User } from "better-auth";
import React from "react";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { But<PERSON> } from "../ui/button";
import { Check, Trash, X } from "lucide-react";

type FriendData = {
  id: string;
  name: string;
  image: string | null;
  bio: string | null;
  sender: string;
  receiver: string;
};

type RequestsProps = {
  user: User;
  requests: FriendData[];
  loading: boolean;
  forMe: boolean;
  onAcceptRequest: (senderId: string) => Promise<boolean>;
  onDenyRequest: (senderId: string, receiverId: string) => Promise<boolean>;
};

export default ({ user, requests, loading, forMe, onAcceptRequest, onDenyRequest }: RequestsProps) => {
  if (loading) return <div className="text-center text-muted-foreground">Loading...</div>;
  if (requests.length === 0)
    return <div className="text-center text-muted-foreground">{forMe ? "No pending requests" : "No sent requests"}</div>;
  return (
    <ul className="flex flex-col gap-2 h-full overflow-y-auto">
      {requests.map((f) => (
        <li key={f.id} className="flex items-center justify-between gap-2 p-2 rounded-lg hover:bg-muted/50 transition-colors">
          <div className="flex items-center gap-2">
            <Avatar>
              <AvatarImage src={f.image || ""} alt={`${f.name}'s profile picture`} />
              <AvatarFallback>{f.name?.charAt(0)?.toUpperCase() || "?"}</AvatarFallback>
            </Avatar>
            <span className="truncate font-medium">{f.name}</span>
          </div>
          <div className="flex flex-row gap-1">
            {forMe && (
              <Button
                size="sm"
                onClick={async () => {
                  await onAcceptRequest(f.id);
                }}>
                <Check />
              </Button>
            )}
            <Button
              variant={"destructive"}
              size="sm"
              onClick={async () => {
                if (forMe) await onDenyRequest(f.id, user.id);
                else await onDenyRequest(user.id, f.id);
              }}>
              {forMe ? <X /> : <Trash />}
            </Button>
          </div>
        </li>
      ))}
    </ul>
  );
};
