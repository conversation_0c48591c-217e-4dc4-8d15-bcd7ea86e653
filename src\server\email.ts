"use server";

import { JSXElementConstructor, ReactElement } from "react";
import { CreateEmailResponseSuccess, ErrorResponse, Resend } from "resend";

const resend = new Resend(process.env.RESEND_API_KEY);

export const sentEmail = async (
  to: string | string[],
  subject: string,
  object: ReactElement<unknown, string | JSXElementConstructor<any>>,
  from: string = ""
): Promise<
  | {
      status: 200;
      response: CreateEmailResponseSuccess;
    }
  | {
      status: 500;
      response: ErrorResponse;
    }
> => {
  const { data, error } = await resend.emails.send({
    from: from === "" ? "Mystique <<EMAIL>>" : from,
    to: to,
    subject: subject,
    react: object,
  });
  if (error || !data) return { status: 500, response: error };
  return { status: 200, response: data };
};
