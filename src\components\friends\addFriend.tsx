"use client";

import { User } from "better-auth";
import { <PERSON><PERSON> } from "../ui/button";
import * as D from "@/components/ui/dialog";
import { Input } from "../ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Search, UserPlus } from "lucide-react";
import React from "react";
import { searchUsers } from "@/server/friend";

type SearchResult = {
  id: string;
  name: string;
  email: string;
  image: string | null;
  bio: string | null;
};

type AddFriendProps = {
  user: User;
  onSendRequest: (receiverId: string) => Promise<boolean>;
};

export default ({ user, onSendRequest }: AddFriendProps) => {
  const [query, setQuery] = React.useState<string>("");
  const [open, setOpen] = React.useState<boolean>(false);
  const [loading, setLoading] = React.useState<boolean>(false);
  const [searching, setSearching] = React.useState<boolean>(false);
  const [searchResults, setSearchResults] = React.useState<SearchResult[]>([]);

  React.useEffect(() => {
    if (query.length < 4) {
      setSearchResults([]);
      return;
    }

    const timeoutId = setTimeout(async () => {
      setSearching(true);
      try {
        const results = await searchUsers(query, user.id);
        setSearchResults(results);
      } catch (error) {
        console.error("Search error:", error);
        setSearchResults([]);
      }
      setSearching(false);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [query, user.id]);

  const handleSendRequest = async (receiverId: string) => {
    setLoading(true);
    const success = await onSendRequest(receiverId);
    if (success) {
      setQuery("");
      setSearchResults([]);
      setOpen(false);
    }
    setLoading(false);
  };

  const handleReset = () => {
    setQuery("");
    setSearchResults([]);
    setLoading(false);
    setSearching(false);
  };

  return (
    <D.Dialog
      open={open}
      onOpenChange={(newOpen) => {
        setOpen(newOpen);
        if (!newOpen) {
          handleReset();
        }
      }}>
      <D.DialogTrigger asChild>
        <Button>
          <UserPlus className="w-4 h-4 mr-2" />
          Add Friend
        </Button>
      </D.DialogTrigger>
      <D.DialogContent className="max-w-md">
        <D.DialogHeader>
          <D.DialogTitle>Add New Friend</D.DialogTitle>
          <D.DialogDescription>Search by name, email, or ID to find and add friends</D.DialogDescription>
        </D.DialogHeader>

        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            type="text"
            placeholder="Search by name, email, or ID..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {searching && <div className="text-center text-muted-foreground py-4">Searching...</div>}

        {searchResults.length > 0 && (
          <div className="max-h-60 overflow-y-auto space-y-2">
            {searchResults.map((result) => (
              <div key={result.id} className="flex items-center justify-between p-3 rounded-lg border hover:bg-muted/50 transition-colors">
                <div className="flex items-center gap-3">
                  <Avatar>
                    <AvatarImage src={result.image || ""} alt={`${result.name}'s profile`} />
                    <AvatarFallback>{result.name?.charAt(0)?.toUpperCase() || "?"}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium truncate">{result.name}</p>
                    <p className="text-sm text-muted-foreground truncate">{result.email}</p>
                    {result.bio && <p className="text-xs text-muted-foreground truncate mt-1">{result.bio}</p>}
                  </div>
                </div>
                <Button size="sm" onClick={() => handleSendRequest(result.id)} disabled={loading}>
                  {loading ? "Sending..." : "Add"}
                </Button>
              </div>
            ))}
          </div>
        )}

        {query.length >= 4 && !searching && searchResults.length === 0 && (
          <div className="text-center text-muted-foreground py-4">No users found matching "{query}"</div>
        )}

        {query.length < 4 && <div className="text-center text-muted-foreground py-4">Type at least 4 characters to search</div>}
      </D.DialogContent>
    </D.Dialog>
  );
};
