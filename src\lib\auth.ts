import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { db } from "@/db";
import * as schema from "@/db/schema";
import { redis } from "./redis";
import React from "react";
import { sentEmail } from "@/server/email";

export const auth = betterAuth({
  database: drizzleAdapter(db, {
    provider: "pg",
    schema: schema,
  }),
  user: {
    changeEmail: {
      enabled: true,
      sendChangeEmailVerification: async ({ user, newEmail, url, token }, request) => {
        const subject = "Confirm your new email";
        const body = React.createElement(
          React.Fragment,
          null,
          React.createElement(
            "div",
            { style: { fontFamily: "system-ui, sans-serif", lineHeight: 1.4 } },
            React.createElement("h1", null, "Confirm your new email"),
            React.createElement("p", null, `Hi ${user.name || user.email},`),
            React.createElement("p", null, "We received a request to change your account email to:"),
            React.createElement("p", null, React.createElement("strong", null, newEmail)),
            React.createElement("p", null, React.createElement("a", { href: url }, "Confirm email change")),
            React.createElement("p", null, "If you didn't request this, you can ignore this email."),
            React.createElement("hr", null),
            React.createElement("small", null, `Token: ${token}`)
          )
        );
        await sentEmail(newEmail, subject, body, "Mystique <<EMAIL>>");
      },
    },
  },
  secret: process.env.BETTER_AUTH_SECRET as string,
  baseURL: process.env.BETTER_AUTH_URL as string,
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false,
    minPasswordLength: 8,
    maxPasswordLength: 128,
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
    cookieCache: {
      enabled: true,
      maxAge: 60 * 5, // 5 minutes
    },
  },
  socialProviders: {
    github: {
      clientId: process.env.GITHUB_CLIENT_ID as string,
      clientSecret: process.env.GITHUB_CLIENT_SECRET as string,
    },
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
    },
  },
  secondaryStorage: {
    get: async (key) => {
      try {
        return await redis.get(key);
      } catch (error) {
        console.error("Redis get error:", error);
        return null;
      }
    },
    set: async (key, value, ttl) => {
      try {
        if (ttl) await redis.set(key, value, { EX: ttl });
        else await redis.set(key, value);
      } catch (error) {
        console.error("Redis set error:", error);
      }
    },
    delete: async (key) => {
      try {
        await redis.del(key);
      } catch (error) {
        console.error("Redis delete error:", error);
      }
    },
  },
});
