{"name": "mystique", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "socket-messages": "tsx src/sockets/messages.ts"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-context-menu": "^2.2.16", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.16", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.8", "@react-email/render": "^1.3.1", "@types/socket.io": "^3.0.1", "better-auth": "^1.3.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "crypto": "^1.0.1", "date-fns": "^4.1.0", "dotenv": "^17.2.2", "drizzle-orm": "^0.44.5", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "^0.543.0", "minio": "^8.0.6", "next": "15.5.2", "next-rspack": "15.5.2", "next-themes": "^0.4.6", "pg": "^8.16.3", "react": "19.1.0", "react-day-picker": "^9.9.0", "react-dom": "19.1.0", "react-easy-crop": "^5.5.2", "react-hook-form": "^7.62.0", "react-resizable-panels": "^3.0.5", "recharts": "^2.15.4", "redis": "^5.8.2", "resend": "^6.1.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zod": "^4.1.5"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/pg": "^8.15.5", "@types/react": "^19", "@types/react-dom": "^19", "@types/redis": "^4.0.10", "concurrently": "^9.2.1", "drizzle-kit": "^0.31.4", "prettier": "3.6.2", "tailwindcss": "^4", "tsx": "^4.20.5", "tw-animate-css": "^1.3.8", "typescript": "^5"}}