"use client";

import { User } from "better-auth";
import React from "react";
import { getFriends, getForMeRequests, getFromMeRequests, sendRequest, acceptRequest, denyRequest, searchUsers } from "@/server/friend";
import { toast } from "../ui/toast";
import AddFriend from "./addFriend";
import Friends from "./friends";
import GetId from "./getId";
import Requests from "./requests";
import { Button } from "../ui/button";
import Header from "../layout/header";

export type FriendData = {
  id: string;
  email: string;
  name: string;
  image: string | null;
  bio: string | null;
  sender: string;
  receiver: string;
};

export default ({ user }: { user: User }) => {
  const [friends, setFriends] = React.useState<FriendData[]>([]);
  const [requestsForMe, setRequestsForMe] = React.useState<FriendData[]>([]);
  const [requestsFromMe, setRequestsFromMe] = React.useState<FriendData[]>([]);
  const [loading, setLoading] = React.useState<boolean>(true);

  const loadAllData = async () => {
    setLoading(true);
    try {
      const [friendsData, forMeData, fromMeData] = await Promise.all([
        getFriends(user.id),
        getForMeRequests(user.id),
        getFromMeRequests(user.id),
      ]);
      setFriends(friendsData);
      setRequestsForMe(forMeData);
      setRequestsFromMe(fromMeData);
    } catch (error) {
      toast.error("Failed to load friends data");
    }
    setLoading(false);
  };

  React.useEffect(() => {
    loadAllData();
  }, [user.id]);

  const handleSendRequest = async (receiverId: string) => {
    try {
      await sendRequest(user.id, receiverId);
      await loadAllData();
      toast.success("Friend request sent!");
      return true;
    } catch (e: any) {
      toast.error(e.message || "Failed to send friend request");
      return false;
    }
  };

  const handleAcceptRequest = async (senderId: string) => {
    try {
      await acceptRequest(senderId, user.id);
      await loadAllData();
      toast.success("Friend request accepted");
      return true;
    } catch (error) {
      toast.error("Error during accepting friend request");
      return false;
    }
  };

  const handleDenyRequest = async (senderId: string, receiverId: string) => {
    try {
      await denyRequest(senderId, receiverId);
      await loadAllData();
      toast.success("Friend request denied");
      return true;
    } catch (error) {
      toast.error("Error during denying friend request");
      return false;
    }
  };

  return (
    <div className="relative">
      <Header
        title={"Friends"}
        description={"Manage your friends and friend requests."}
        additional={
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sticky top- pb-2">
            <GetId user={user} />
            <AddFriend user={user} onSendRequest={handleSendRequest} />
            <Button onClick={loadAllData}>Refresh</Button>
          </div>
        }
      />
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <section>
          <h1 className="text-center text-xl font-semibold mb-2">Friends</h1>
          <Friends friends={friends} loading={loading} onDenyRequest={handleDenyRequest} />
        </section>
        <section>
          <h1 className="text-center text-xl font-semibold mb-2">My requests</h1>
          <Requests
            user={user}
            requests={requestsForMe}
            loading={loading}
            forMe={true}
            onAcceptRequest={handleAcceptRequest}
            onDenyRequest={handleDenyRequest}
          />
        </section>
        <section>
          <h1 className="text-center text-xl font-semibold mb-2">Pending requests</h1>
          <Requests
            user={user}
            requests={requestsFromMe}
            loading={loading}
            forMe={true}
            onAcceptRequest={handleAcceptRequest}
            onDenyRequest={handleDenyRequest}
          />
        </section>
      </div>
    </div>
  );
};

/*
<section>
        <h1 className="text-center text-xl font-semibold mb-4">Utility</h1>
        <Separator className="my-2 py-0.5 hidden md:block" />
        <div className="pr-3 pt-2 flex flex-col gap-4">
          <GetId user={user} />
          <AddFriend user={user} onSendRequest={handleSendRequest} />
          <Button onClick={loadAllData}>Refresh</Button>
        </div>
      </section>
      <Separator orientation="vertical" className="my-1 px-0.5 hidden md:block" />
      <section>
        <h1 className="text-center text-xl font-semibold mb-4">Friends</h1>
        <Separator className="mt-2 py-0.5 mb-3 hidden md:block" />
        <Friends friends={friends} loading={loading} onDenyRequest={handleDenyRequest} />
      </section>
      <Separator orientation="vertical" className="my-1 px-0.5 hidden md:block" />
      <section>
        <div className="h-1/2 mb-4">
          <h1 className="text-center text-xl font-semibold mb-4">My Requests</h1>
          <Separator className="mt-2 mb-3 py-0.5 hidden md:block" />
          <Requests
            user={user}
            requests={requestsFromMe}
            loading={loading}
            forMe={false}
            onAcceptRequest={handleAcceptRequest}
            onDenyRequest={handleDenyRequest}
          />
        </div>
        <div className="h-1/2">
          <h1 className="text-center text-xl font-semibold mb-4">Requests for Me</h1>
          <Separator className="mt-2 mb-3 py-0.5 hidden md:block" />
          <Requests
            user={user}
            requests={requestsForMe}
            loading={loading}
            forMe={true}
            onAcceptRequest={handleAcceptRequest}
            onDenyRequest={handleDenyRequest}
          />
        </div>
      </section>
*/
