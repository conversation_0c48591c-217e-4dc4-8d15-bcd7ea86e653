"use client";

import { But<PERSON> } from "../ui/button";
import * as D from "@/components/ui/dialog";
import { hasPassword, setNewPassword, updatePassword } from "@/server/user-crud";
import React from "react";
import { toast } from "../ui/toast";
import { LucideCheck, LucideX } from "lucide-react";
import { PasswordInput } from "../layout/password-input";
import { User } from "better-auth";

export default ({ user, headers }: { user: User; headers: Headers }) => {
  const [hasPswd, setHas] = React.useState<boolean | undefined>(undefined);
  const [open, setOpen] = React.useState<boolean>(false);
  const [password, setPassword] = React.useState<string>("");
  const [confirmPassword, setConfirmPassword] = React.useState<string>("");
  const [currentPassword, setCurrentPassword] = React.useState<string>("");
  const [loading, setLoading] = React.useState<boolean>(false);
  const [revoke, setRevoke] = React.useState<boolean>(false);

  const reset = () => {
    setPassword("");
    setConfirmPassword("");
    setCurrentPassword("");
    setOpen(false);
  };

  React.useEffect(() => {
    (async () => setHas(await hasPassword(user.id)))();
  }, [user.id]);

  if (hasPswd === undefined)
    return (
      <Button className="w-full" disabled>
        Loading...
      </Button>
    );

  return (
    <D.Dialog open={open} onOpenChange={setOpen}>
      <D.DialogTrigger asChild className="w-full">
        <Button className="w-full" variant="default">
          {hasPswd ? "Change Password" : "Set Password"}
        </Button>
      </D.DialogTrigger>
      <D.DialogContent>
        <D.DialogHeader>
          <D.DialogTitle>{hasPswd ? "Change Password" : "Set Password"}</D.DialogTitle>
          <D.DialogDescription>Enter your new password below.</D.DialogDescription>
        </D.DialogHeader>
        {hasPswd ? (
          <>
            <PasswordInput
              placeholder="Current Password"
              value={currentPassword}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setCurrentPassword(e.target.value)}
            />
            <PasswordInput
              placeholder="New Password"
              value={password}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setPassword(e.target.value)}
            />
            <PasswordInput
              placeholder="Confirm New Password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
            />
            <div className="flex flex-row gap-2 justify-end">
              <Button onClick={() => setRevoke(!revoke)}>{revoke ? <LucideCheck /> : <LucideX />} Revoke Sessions</Button>
              <Button
                disabled={loading}
                onClick={async () => {
                  setLoading(true);
                  try {
                    if (!currentPassword) throw new Error("Current password is required");
                    if (password !== confirmPassword) throw new Error("Passwords do not match");
                    if (password.length < 8) throw new Error("Password must be at least 8 characters long");
                    if (password.length > 128) throw new Error("Password cannot be longer than 128 characters");
                    await updatePassword(currentPassword, password, headers, true);
                    toast.success("Password changed successfully");
                    reset();
                  } catch (e: any) {
                    toast.error(e.message);
                  }
                  setLoading(false);
                }}>
                Change password
              </Button>
            </div>
          </>
        ) : (
          <>
            <PasswordInput
              placeholder="New Password"
              value={password}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setPassword(e.target.value)}
            />
            <PasswordInput
              placeholder="Confirm Password"
              value={confirmPassword}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setConfirmPassword(e.target.value)}
            />
            <Button
              disabled={loading}
              className="w-full"
              onClick={async () => {
                setLoading(true);
                try {
                  if (password !== confirmPassword) throw new Error("Passwords do not match");
                  if (password.length < 8) throw new Error("Password must be at least 8 characters long");
                  if (password.length > 128) throw new Error("Password cannot be longer than 128 characters");
                  await setNewPassword(password, headers);
                  toast.success("Password set successfully");
                  reset();
                  setHas(true);
                } catch (e: any) {
                  toast.error(e.message);
                  return;
                }
                setLoading(false);
              }}>
              Set password
            </Button>
          </>
        )}
      </D.DialogContent>
    </D.Dialog>
  );
};
