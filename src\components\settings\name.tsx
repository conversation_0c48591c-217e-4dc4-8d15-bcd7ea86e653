"use client";

import React from "react";
import { Input } from "../ui/input";
import { updateName } from "@/server/user-crud";
import { toast } from "../ui/toast";
import { User } from "better-auth";
import { authClient } from "@/lib/auth-client";

export default ({ user, className }: { user: User; className?: string }) => {
  const [name, setName] = React.useState(user.name);
  const [originalName, setOriginalName] = React.useState(user.name);
  const [saving, setSaving] = React.useState(false);
  const [secondsLeft, setSecondsLeft] = React.useState<number | null>(null);
  const saveTimeout = React.useRef<NodeJS.Timeout | null>(null);
  const intervalRef = React.useRef<NodeJS.Timeout | null>(null);

  const isTooShort = name.length < 6;
  const isTooLong = name.length > 64;

  const update = async () => {
    if (saveTimeout.current) clearTimeout(saveTimeout.current);
    if (intervalRef.current) clearInterval(intervalRef.current);
    if (saving || name === originalName) return;
    if (isTooShort || isTooLong) return;

    setSaving(true);
    try {
      await updateName(user.id, name);
      setOriginalName(name);
      await authClient.updateUser({ name });
      toast.success("Name was saved!");
    } catch (err) {
      toast.error("Failed to save name");
    }
    setSaving(false);
    setSecondsLeft(null);
  };

  React.useEffect(() => {
    if (name === originalName || saving || isTooShort || isTooLong) {
      setSecondsLeft(null);
      if (intervalRef.current) clearInterval(intervalRef.current);
      return;
    }

    setSecondsLeft(3);

    if (saveTimeout.current) clearTimeout(saveTimeout.current);
    if (intervalRef.current) clearInterval(intervalRef.current);

    intervalRef.current = setInterval(() => {
      setSecondsLeft((prev) => {
        if (prev === null) return null;
        if (prev <= 1) {
          clearInterval(intervalRef.current!);
          return null;
        }
        return prev - 1;
      });
    }, 1000);

    saveTimeout.current = setTimeout(async () => {
      update();
    }, 3000);

    return () => {
      if (saveTimeout.current) clearTimeout(saveTimeout.current);
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, [name, saving, originalName, user.id, isTooShort, isTooLong]);

  return (
    <div className={className}>
      <span className="flex flex-row justify-between items-center text-sm">
        <span className="font-semibold text-lg">
          Name{" "}
          <span className={`font-light ${isTooShort || isTooLong ? "text-destructive" : "text-muted-foreground"}`}>({name.length}/64)</span>
        </span>
        <span
          className={`${secondsLeft || isTooShort || isTooLong ? "opacity-100" : "opacity-10"} transition-all duration-150 text-sm ${
            isTooShort || isTooLong ? "text-destructive" : "text-muted-foreground"
          }`}>
          {isTooShort
            ? "Too short"
            : isTooLong
              ? "Too long"
              : secondsLeft
                ? `Saving in ${secondsLeft} second${secondsLeft !== 1 ? "s" : ""}...`
                : "No changes"}
        </span>
      </span>
      <Input
        placeholder="Name"
        value={name}
        disabled={saving}
        onChange={(e) => setName(e.target.value)}
        onKeyDown={(e) => {
          if (e.key === "Enter" && e.ctrlKey) {
            e.preventDefault();
            update();
          }
          if (e.key.toLowerCase() === "z" && e.ctrlKey) {
            e.preventDefault();
            setName(originalName);
          }
        }}
      />
    </div>
  );
};
