"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import { Slider } from "@/components/ui/slider";
import { User } from "better-auth";
import { uploadAvatar } from "@/server/profile-picture";
import { useAvatar } from "@/components/layout/avatar-provider";
import Cropper, { type Area } from "react-easy-crop";
import Avatar from "../layout/avatar";
import { Camera } from "lucide-react";
import { toast } from "../ui/toast";
import React from "react";

const getCroppedImg = async (imageSrc: string, crop: { width: number; height: number; x: number; y: number }): Promise<Blob> => {
  const image = new Image();
  image.src = imageSrc;

  await new Promise((resolve) => {
    image.onload = resolve;
  });

  const canvas = document.createElement("canvas");
  canvas.width = crop.width;
  canvas.height = crop.height;
  const ctx = canvas.getContext("2d") as CanvasRenderingContext2D;
  ctx.drawImage(image, crop.x, crop.y, crop.width, crop.height, 0, 0, crop.width, crop.height);

  return new Promise((resolve) => {
    canvas.toBlob((blob) => {
      if (blob) resolve(blob);
    }, "image/png");
  });
};

export default ({ user }: { user: User }) => {
  const { setImage } = useAvatar();
  const inputRef = React.useRef<HTMLInputElement>(null);
  const [imageSrc, setImageSrc] = React.useState<string | null>(null);
  const [crop, setCrop] = React.useState({ x: 0, y: 0 });
  const [zoom, setZoom] = React.useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = React.useState<Area | null>(null);
  const [loading, setLoading] = React.useState(false);

  return (
    <div className="w-full flex items-center justify-center">
      <Dialog>
        <DialogTrigger className="size-full my-2 sm:mt-0 md:hover:scale-101 transition-all duration-150 relative group flex items-center justify-center">
          <Camera className="absolute z-10 size-14 left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 stroke-background fill-foreground stroke-2" />
          <Avatar size="lg" />
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Upload and Crop Avatar</DialogTitle>
          </DialogHeader>
          <button
            type="button"
            className="group w-full mt-4 border-3 border-dashed rounded-lg hover:border-black/20 transition-colors duration-200 aspect-square relative bg-muted overflow-hidden select-none"
            onClick={() => {
              if (imageSrc === null) inputRef.current?.click();
            }}
            aria-label="Select image to upload">
            {imageSrc ? (
              <Cropper
                image={imageSrc}
                crop={crop}
                zoom={zoom}
                aspect={1}
                onCropChange={setCrop}
                onZoomChange={setZoom}
                onCropComplete={(_: Area, croppedAreaPixels: Area) => {
                  setCroppedAreaPixels(croppedAreaPixels);
                }}
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-muted-foreground">
                <div className="relative font-semibold flex items-center gap-4 transition-all duration-300 text-foreground/60 after:absolute after:top-5 after:bg-black/20 after:left-0 after:h-0.5 after:w-0 after:rounded-xl after:transition-[width] after:duration-500 group-hover:after:w-full group-hover:font-bold group-hover:scale-110">
                  No image selected
                </div>
              </div>
            )}
          </button>

          <div className="w-full flex flex-row gap-2 justify-between items-center">
            <label htmlFor="zoom-slider" className="select-none">
              Zoom:
            </label>
            <Slider
              id="zoom-slider"
              min={1}
              max={3}
              step={0.1}
              value={[zoom]}
              onValueChange={(val) => setZoom(val[0])}
              className="my-2 border"
            />
          </div>

          <input
            ref={inputRef}
            type="file"
            accept="image/*"
            onChange={async (e: React.ChangeEvent<HTMLInputElement>) => {
              const file = e.target.files?.[0];
              if (file) {
                const reader = new FileReader();
                reader.onload = () => setImageSrc(reader.result as string);
                reader.readAsDataURL(file);
              }
            }}
            className="hidden"
          />
          <Button
            onClick={async () => {
              if (!imageSrc || !croppedAreaPixels) return;
              setLoading(true);
              try {
                const croppedBlob = await getCroppedImg(imageSrc, croppedAreaPixels);
                const formData = new FormData();
                formData.append("userId", user.id);
                formData.append("file", croppedBlob, "avatar.png");
                const result = await uploadAvatar(formData);
                if (result.success && result.path) {
                  const event = new CustomEvent("avatarUpdated", {
                    detail: { userId: user.id, imagePath: result.path },
                  });
                  setImage(`${result.path}?t=${Date.now()}`);
                  window.dispatchEvent(event);
                  toast.success("Profile picture updated successfully!");
                  setImageSrc(null);
                  setCrop({ x: 0, y: 0 });
                  setZoom(1);
                  setCroppedAreaPixels(null);
                } else toast.error("Failed to upload profile picture. Please try again.");
              } catch (error) {
                console.error("Error uploading avatar:", error);
                toast.error("An error occurred while uploading. Please try again.");
              } finally {
                setLoading(false);
              }
            }}
            disabled={!imageSrc || loading}>
            {loading ? "Uploading..." : "Upload"}
          </Button>
        </DialogContent>
      </Dialog>
    </div>
  );
};
